plugins {
    id "fabric-loom" version "1.10-SNAPSHOT"
}

base {
    archivesBaseName = project.archives_base_name
    version = project.mod_version
    group = project.maven_group
}

repositories {
    exclusiveContent {
        forRepository {
            maven {
                name = "Modrinth"
                url = uri("https://api.modrinth.com/maven")
            }
        }
        filter {
            includeGroup("maven.modrinth")
        }
    }


    maven {
        name = "Meteor Dev Releases"
        url = "https://maven.meteordev.org/releases"
    }
    maven {
        name = "Meteor Dev Snapshots"
        url = "https://maven.meteordev.org/snapshots"
    }
    maven { url = "https://masa.dy.fi/maven" }

    maven { url = "https://maven.terraformersmc.com/releases/" }
    maven {
        url = "https://jitpack.io"
    }
}

dependencies {
    // Fabric
    minecraft "com.mojang:minecraft:${project.minecraft_version}"
    mappings "net.fabricmc:yarn:${project.yarn_mappings}:v2"
    modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"

//    modImplementation "maven.modrinth:litematica:0.19.59"
    modImplementation("com.github.sakura-ryoko:malilib:1.21-0.21.9")
    modImplementation("com.github.sakura-ryoko:litematica:1.21-0.19.59")


    // Meteor
    modImplementation "meteordevelopment:meteor-client:${project.meteor_version}"

    modCompileOnly "meteordevelopment:baritone:1.21.1-SNAPSHOT"
}

tasks {
    processResources {
        def propertyMap = [
            "version"   : project.version,
            "mc_version": project.minecraft_version,
        ]

        filesMatching("fabric.mod.json") {
            expand(propertyMap)
        }
    }

    jar {
        from("LICENSE") {
            rename { "${it}_${project.base.archivesBaseName}" }
        }
    }

    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }

    tasks.withType(JavaCompile).configureEach {
        it.options.encoding = "UTF-8"
        it.options.release = 21
    }
}
